const { Client } = require('pg')

async function testConnection() {
  console.log('🔍 Testing direct PostgreSQL connection...')
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL || "postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require"
  })
  
  try {
    await client.connect()
    console.log('✅ Database connection successful!')
    
    // Test a simple query
    const result = await client.query('SELECT COUNT(*) as count FROM users')
    console.log(`📊 Found ${result.rows[0].count} users in database`)
    
    // Test finding a specific user
    const userResult = await client.query('SELECT name, role FROM users WHERE phone = $1', ['+998901234568'])
    if (userResult.rows.length > 0) {
      console.log(`👤 Found test user: ${userResult.rows[0].name} (${userResult.rows[0].role})`)
    } else {
      console.log('❌ Test user not found')
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
  } finally {
    await client.end()
  }
}

testConnection()
