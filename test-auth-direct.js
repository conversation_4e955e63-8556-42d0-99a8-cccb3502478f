const { Client } = require('pg')
const bcrypt = require('bcryptjs')

async function testAuthDirect() {
  console.log('🔍 Testing direct authentication...')
  
  // Test Reception database
  console.log('\n📱 Testing Reception Database:')
  const receptionClient = new Client({
    connectionString: "postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require"
  })
  
  try {
    await receptionClient.connect()
    
    // Test finding a user
    const userResult = await receptionClient.query('SELECT name, role, password FROM users WHERE phone = $1', ['+998901234568'])
    if (userResult.rows.length > 0) {
      const user = userResult.rows[0]
      console.log(`✅ Found user: ${user.name} (${user.role})`)
      
      // Test password validation
      const isValid = await bcrypt.compare('manager123', user.password)
      console.log(`🔐 Password validation: ${isValid ? 'SUCCESS' : 'FAILED'}`)
    } else {
      console.log('❌ User not found')
    }
    
  } catch (error) {
    console.error('❌ Reception database error:', error.message)
  } finally {
    await receptionClient.end()
  }
  
  // Test Management database
  console.log('\n📱 Testing Management Database:')
  const managementClient = new Client({
    connectionString: "postgresql://crm_owner:<EMAIL>/crm?sslmode=require&channel_binding=require"
  })
  
  try {
    await managementClient.connect()
    
    // Test finding admin user
    const adminResult = await managementClient.query('SELECT name, role, password FROM users WHERE phone = $1', ['+998901234567'])
    if (adminResult.rows.length > 0) {
      const admin = adminResult.rows[0]
      console.log(`✅ Found admin: ${admin.name} (${admin.role})`)
      
      // Test password validation
      const isValid = await bcrypt.compare('admin123', admin.password)
      console.log(`🔐 Password validation: ${isValid ? 'SUCCESS' : 'FAILED'}`)
    } else {
      console.log('❌ Admin user not found')
    }
    
  } catch (error) {
    console.error('❌ Management database error:', error.message)
  } finally {
    await managementClient.end()
  }
  
  console.log('\n✅ Direct authentication test completed!')
}

testAuthDirect()
