import { Client } from 'pg'
import bcrypt from 'bcryptjs'

interface User {
  id: string
  phone: string
  name: string
  email?: string
  role: string
  password: string
}

class DirectDatabaseClient {
  private client: Client

  constructor() {
    this.client = new Client({
      connectionString: process.env.DATABASE_URL
    })
  }

  async connect() {
    if (!this.client._connected) {
      await this.client.connect()
    }
  }

  async disconnect() {
    if (this.client._connected) {
      await this.client.end()
    }
  }

  async findUserByPhone(phone: string): Promise<User | null> {
    await this.connect()
    
    try {
      const result = await this.client.query(
        'SELECT id, phone, name, email, role, password FROM users WHERE phone = $1',
        [phone]
      )
      
      if (result.rows.length === 0) {
        return null
      }
      
      return result.rows[0] as User
    } catch (error) {
      console.error('Database query error:', error)
      throw error
    }
  }

  async validatePassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword)
  }
}

export const dbDirect = new DirectDatabaseClient()
export type { User }
