const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function testConnection() {
  console.log('🔍 Testing database connection...')
  
  try {
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful!')
    
    // Test a simple query
    const userCount = await prisma.user.count()
    console.log(`📊 Found ${userCount} users in database`)
    
    // Test finding a specific user
    const testUser = await prisma.user.findFirst({
      where: { phone: '+998901234568' }
    })
    
    if (testUser) {
      console.log(`👤 Found test user: ${testUser.name} (${testUser.role})`)
    } else {
      console.log('❌ Test user not found')
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    console.error('Full error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
