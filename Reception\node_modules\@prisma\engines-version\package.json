{"name": "@prisma/engines-version", "version": "5.6.0-32.e95e739751f42d8ca026f6b910f5a2dc5adeaeee", "main": "index.js", "types": "index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "prisma": {"enginesVersion": "e95e739751f42d8ca026f6b910f5a2dc5adeaeee"}, "repository": {"type": "git", "url": "https://github.com/prisma/engines-wrapper.git", "directory": "packages/engines-version"}, "devDependencies": {"@types/node": "18.18.9", "typescript": "4.9.5"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc -d"}}