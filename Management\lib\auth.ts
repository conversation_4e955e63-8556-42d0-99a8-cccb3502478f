import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        phone: { label: "Phone", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.phone || !credentials?.password) {
          return null
        }

        try {
          // Admin server: Authenticate directly against database
          const user = await prisma.user.findUnique({
            where: { phone: credentials.phone },
            select: {
              id: true,
              phone: true,
              name: true,
              email: true,
              role: true,
              password: true,
              createdAt: true,
              updatedAt: true,
            },
          })

          if (!user) {
            console.error('User not found:', credentials.phone)
            return null
          }

          // Verify the password
          const isPasswordValid = await bcrypt.compare(credentials.password, user.password)
          if (!isPasswordValid) {
            console.error('Invalid password for user:', credentials.phone)
            return null
          }

          // Verify the user role is allowed on admin server
          const allowedRoles = ['ADMIN', 'MANAGER', 'CASHIER']
          if (!allowedRoles.includes(user.role)) {
            console.error('User role not allowed on admin server:', user.role)
            return null
          }

          return {
            id: user.id,
            phone: user.phone,
            name: user.name,
            email: user.email,
            role: user.role,
          }
        } catch (error) {
          console.error('Error authenticating user:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role || null
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = (token.role as string) || null
      }
      return session
    }
  },
  pages: {
    signIn: "/auth/signin",
  }
}
