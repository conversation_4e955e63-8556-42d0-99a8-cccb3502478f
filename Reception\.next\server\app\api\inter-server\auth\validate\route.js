/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/inter-server/auth/validate/route";
exports.ids = ["app/api/inter-server/auth/validate/route"];
exports.modules = {

/***/ "(rsc)/./app/api/inter-server/auth/validate/route.ts":
/*!*****************************************************!*\
  !*** ./app/api/inter-server/auth/validate/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/inter-server */ \"(rsc)/./lib/inter-server.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n// Inter-Server Authentication Validation Endpoint\n// Allows staff server to validate users against admin server\n\n\n\n\nasync function POST(request) {\n    try {\n        // Validate inter-server authentication\n        if (!(0,_lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.validateInterServerAuth)(request)) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Unauthorized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized inter-server request'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { phone, password } = body;\n        if (!phone || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Phone and password are required'\n            }, {\n                status: 400\n            });\n        }\n        // Find user in database\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                phone\n            },\n            select: {\n                id: true,\n                name: true,\n                phone: true,\n                email: true,\n                role: true,\n                password: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n        if (!user) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'User not found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid credentials'\n            }, {\n                status: 401\n            });\n        }\n        // Verify password\n        const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, user.password);\n        if (!isValidPassword) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Invalid password');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid credentials'\n            }, {\n                status: 401\n            });\n        }\n        // Check if user role is allowed on requesting server\n        const requestingServer = request.headers.get('User-Agent')?.includes('staff') ? 'staff' : 'admin';\n        const staffRoles = [\n            'RECEPTION',\n            'ACADEMIC_MANAGER',\n            'TEACHER',\n            'MANAGER',\n            'STUDENT'\n        ];\n        const adminRoles = [\n            'ADMIN',\n            'CASHIER'\n        ];\n        if (requestingServer === 'staff' && !staffRoles.includes(user.role)) {\n            _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, 'Role not allowed on staff server');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Access denied for this server'\n            }, {\n                status: 403\n            });\n        }\n        // Return user data (excluding password)\n        const { password: _, ...userWithoutPassword } = user;\n        _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', true, `User ${user.id} validated`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: userWithoutPassword\n        });\n    } catch (error) {\n        console.error('Inter-server auth validation error:', error);\n        _lib_inter_server__WEBPACK_IMPORTED_MODULE_1__.InterServerUtils.logRequest('incoming', '/api/inter-server/auth/validate', false, error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Authentication validation failed',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/inter-server/auth/validate/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/inter-server.ts":
/*!*****************************!*\
  !*** ./lib/inter-server.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminServerAPI: () => (/* binding */ AdminServerAPI),\n/* harmony export */   InterServerUtils: () => (/* binding */ InterServerUtils),\n/* harmony export */   StaffServerAPI: () => (/* binding */ StaffServerAPI),\n/* harmony export */   createInterServerHeaders: () => (/* binding */ createInterServerHeaders),\n/* harmony export */   makeInterServerRequest: () => (/* binding */ makeInterServerRequest),\n/* harmony export */   validateInterServerAuth: () => (/* binding */ validateInterServerAuth),\n/* harmony export */   withInterServerAuth: () => (/* binding */ withInterServerAuth)\n/* harmony export */ });\n// Inter-Server Communication Library\n// Handles secure communication between admin and staff servers\n/**\r\n * Validates inter-server request authentication\r\n */ function validateInterServerAuth(request) {\n    const authHeader = request.headers.get('X-Inter-Server-Secret');\n    const timestampHeader = request.headers.get('X-Timestamp');\n    const expectedSecret = process.env.INTER_SERVER_SECRET;\n    if (!authHeader || !expectedSecret) {\n        return false;\n    }\n    // Validate secret\n    if (authHeader !== expectedSecret) {\n        return false;\n    }\n    // Validate timestamp (prevent replay attacks)\n    if (timestampHeader) {\n        const requestTime = parseInt(timestampHeader);\n        const currentTime = Date.now();\n        const maxAge = 5 * 60 * 1000; // 5 minutes\n        if (isNaN(requestTime) || currentTime - requestTime > maxAge) {\n            console.warn('Inter-server request rejected: timestamp too old or invalid');\n            return false;\n        }\n    }\n    return true;\n}\n/**\r\n * Creates authenticated headers for inter-server requests\r\n */ function createInterServerHeaders() {\n    const secret = process.env.INTER_SERVER_SECRET;\n    if (!secret) {\n        throw new Error('INTER_SERVER_SECRET not configured');\n    }\n    const timestamp = Date.now().toString();\n    const serverConfig = InterServerUtils.getServerConfig();\n    const requestId = `${serverConfig.serverType}-${timestamp}-${Math.random().toString(36).substr(2, 9)}`;\n    return {\n        'Content-Type': 'application/json',\n        'X-Inter-Server-Secret': secret,\n        'X-Source-Server': serverConfig.serverType,\n        'X-Request-ID': requestId,\n        'X-Timestamp': timestamp,\n        'User-Agent': `${serverConfig.serverType}-server`\n    };\n}\n/**\r\n * Makes authenticated request to another server\r\n */ async function makeInterServerRequest(targetServer, request) {\n    try {\n        const baseUrl = targetServer === 'admin' ? process.env.ADMIN_SERVER_URL : process.env.STAFF_SERVER_URL;\n        if (!baseUrl) {\n            throw new Error(`${targetServer.toUpperCase()}_SERVER_URL not configured`);\n        }\n        const url = `${baseUrl}${request.endpoint}`;\n        const headers = {\n            ...createInterServerHeaders(),\n            ...request.headers\n        };\n        const response = await fetch(url, {\n            method: request.method,\n            headers,\n            body: request.data ? JSON.stringify(request.data) : undefined\n        });\n        const responseData = await response.json();\n        return {\n            success: response.ok,\n            data: responseData,\n            status: response.status,\n            error: response.ok ? undefined : responseData.error || 'Request failed'\n        };\n    } catch (error) {\n        return {\n            success: false,\n            status: 500,\n            error: error instanceof Error ? error.message : 'Unknown error'\n        };\n    }\n}\n/**\r\n * Middleware for protecting inter-server endpoints\r\n */ function withInterServerAuth(handler) {\n    return async (request, ...args)=>{\n        if (!validateInterServerAuth(request)) {\n            return new Response(JSON.stringify({\n                error: 'Unauthorized inter-server request'\n            }), {\n                status: 401,\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n        }\n        return handler(request, ...args);\n    };\n}\n/**\r\n * Staff server functions - for requesting data from admin server\r\n */ class StaffServerAPI {\n    /**\r\n   * Request user authentication from admin server\r\n   */ static async authenticateUser(phone, password) {\n        return makeInterServerRequest('admin', {\n            endpoint: '/api/inter-server/auth/validate',\n            method: 'POST',\n            data: {\n                phone,\n                password\n            }\n        });\n    }\n    /**\r\n   * Get user data from admin server\r\n   */ static async getUserData(userId) {\n        return makeInterServerRequest('admin', {\n            endpoint: `/api/inter-server/users/${userId}`,\n            method: 'GET'\n        });\n    }\n    /**\r\n   * Sync data with admin server\r\n   */ static async syncData(dataType, data) {\n        return makeInterServerRequest('admin', {\n            endpoint: '/api/inter-server/sync',\n            method: 'POST',\n            data: {\n                type: dataType,\n                data\n            }\n        });\n    }\n}\n/**\r\n * Admin server functions - for handling requests from staff server\r\n */ class AdminServerAPI {\n    /**\r\n   * Validate staff server request\r\n   */ static async validateStaffRequest(request) {\n        return validateInterServerAuth(request);\n    }\n    /**\r\n   * Send data to staff server\r\n   */ static async sendToStaff(endpoint, data) {\n        return makeInterServerRequest('staff', {\n            endpoint,\n            method: 'POST',\n            data\n        });\n    }\n    /**\r\n   * Broadcast update to staff server\r\n   */ static async broadcastUpdate(updateType, data) {\n        return makeInterServerRequest('staff', {\n            endpoint: '/api/inter-server/updates',\n            method: 'POST',\n            data: {\n                type: updateType,\n                data\n            }\n        });\n    }\n}\n/**\r\n * Common utilities for both servers\r\n */ class InterServerUtils {\n    /**\r\n   * Log inter-server communication\r\n   */ static logRequest(direction, endpoint, success, details) {\n        const timestamp = new Date().toISOString();\n        const serverType = process.env.SERVER_TYPE || 'unknown';\n        console.log(`[${timestamp}] Inter-Server ${direction.toUpperCase()}: ${endpoint}`, {\n            server: serverType,\n            success,\n            details\n        });\n    }\n    /**\r\n   * Check if current server can communicate with target server\r\n   */ static async healthCheck(targetServer) {\n        try {\n            const response = await makeInterServerRequest(targetServer, {\n                endpoint: '/api/health',\n                method: 'GET'\n            });\n            return response.success;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\r\n   * Get server configuration\r\n   */ static getServerConfig() {\n        return {\n            serverType: process.env.SERVER_TYPE || 'staff',\n            adminUrl: process.env.ADMIN_SERVER_URL || 'http://localhost:3001',\n            staffUrl: process.env.STAFF_SERVER_URL || 'http://localhost:3000',\n            hasInterServerSecret: !!process.env.INTER_SERVER_SECRET\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/inter-server.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXFDLEVBQUVILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxDUk0tUGxhdGZvcm1cXFJlY2VwdGlvblxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXHJcblxyXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xyXG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcclxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_CRM_Platform_Reception_app_api_inter_server_auth_validate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/inter-server/auth/validate/route.ts */ \"(rsc)/./app/api/inter-server/auth/validate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/inter-server/auth/validate/route\",\n        pathname: \"/api/inter-server/auth/validate\",\n        filename: \"route\",\n        bundlePath: \"app/api/inter-server/auth/validate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CRM-Platform\\\\Reception\\\\app\\\\api\\\\inter-server\\\\auth\\\\validate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_CRM_Platform_Reception_app_api_inter_server_auth_validate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&page=%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Finter-server%2Fauth%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5CCRM-Platform%5CReception&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();