const { PrismaClient } = require('@prisma/client')
const { execSync } = require('child_process')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function resetDatabase() {
  console.log('🔄 Resetting database...')
  
  try {
    // Reset database by pushing schema (this will recreate all tables)
    console.log('📋 Pushing schema to reset database...')
    execSync('npx prisma db push --force-reset', { stdio: 'inherit' })
    
    console.log('✅ Database reset completed!')
    return true
  } catch (error) {
    console.error('❌ Database reset failed:', error.message)
    return false
  }
}

async function seedDatabase() {
  console.log('🌱 Seeding database with test data...')
  
  try {
    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 10)
    const adminUser = await prisma.user.create({
      data: {
        phone: '+998901234567',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'ADMIN',
        password: adminPassword,
      },
    })
    console.log('👤 Created admin user')

    // Create manager user
    const managerPassword = await bcrypt.hash('manager123', 10)
    const managerUser = await prisma.user.create({
      data: {
        phone: '+998901234568',
        name: 'Manager User',
        email: '<EMAIL>',
        role: 'MANAGER',
        password: managerPassword,
      },
    })
    console.log('👤 Created manager user')

    // Create Arevik - Reception user
    const arevikPassword = await bcrypt.hash('Arevik0106$', 10)
    const arevikUser = await prisma.user.create({
      data: {
        phone: '+998912345678',
        name: 'Arevik',
        email: '<EMAIL>',
        role: 'RECEPTION',
        password: arevikPassword,
      },
    })
    console.log('👤 Created Arevik (reception) user')

    // Create academic manager user
    const academicPassword = await bcrypt.hash('academic123', 10)
    const academicUser = await prisma.user.create({
      data: {
        phone: '+998901234570',
        name: 'Academic Manager',
        email: '<EMAIL>',
        role: 'ACADEMIC_MANAGER',
        password: academicPassword,
      },
    })
    console.log('👤 Created academic manager user')

    // Create branches
    const mainBranch = await prisma.branch.create({
      data: {
        name: 'Main',
        address: 'Tashkent, Main Street 123',
        phone: '+998712345678',
        isActive: true,
      },
    })

    const chilonzorBranch = await prisma.branch.create({
      data: {
        name: 'Chilonzor',
        address: 'Tashkent, Chilonzor District',
        phone: '+998712345679',
        isActive: true,
      },
    })

    const yunusobodBranch = await prisma.branch.create({
      data: {
        name: 'Yunusobod',
        address: 'Tashkent, Yunusobod District',
        phone: '+998712345680',
        isActive: true,
      },
    })
    console.log('🏢 Created branches')

    // Create courses
    const courses = [
      { name: 'English Elementary', description: 'Basic English course', duration: 3, price: 500000 },
      { name: 'English Intermediate', description: 'Intermediate English course', duration: 4, price: 600000 },
      { name: 'English Advanced', description: 'Advanced English course', duration: 5, price: 700000 },
      { name: 'IELTS Preparation', description: 'IELTS exam preparation', duration: 3, price: 800000 },
      { name: 'Russian Language', description: 'Russian language course', duration: 4, price: 450000 },
      { name: 'German Language', description: 'German language course', duration: 5, price: 650000 },
    ]

    const createdCourses = []
    for (const course of courses) {
      const createdCourse = await prisma.course.create({
        data: {
          ...course,
          branchId: mainBranch.id,
          isActive: true,
        },
      })
      createdCourses.push(createdCourse)
    }
    console.log('📚 Created courses')

    // Create teachers
    const teachers = [
      { name: 'John Smith', phone: '+998901111111', email: '<EMAIL>', tier: 'JUNIOR', salary: 3000000 },
      { name: 'Sarah Johnson', phone: '+998901111112', email: '<EMAIL>', tier: 'MIDDLE', salary: 4000000 },
      { name: 'Michael Brown', phone: '+998901111113', email: '<EMAIL>', tier: 'SENIOR', salary: 5000000 },
      { name: 'Emma Wilson', phone: '+998901111114', email: '<EMAIL>', tier: 'LEAD', salary: 6000000 },
      { name: 'David Davis', phone: '+998901111115', email: '<EMAIL>', tier: 'JUNIOR', salary: 3000000 },
    ]

    const createdTeachers = []
    for (const teacher of teachers) {
      const teacherPassword = await bcrypt.hash('teacher123', 10)
      const teacherUser = await prisma.user.create({
        data: {
          phone: teacher.phone,
          name: teacher.name,
          email: teacher.email,
          role: 'TEACHER',
          password: teacherPassword,
        },
      })

      const createdTeacher = await prisma.teacher.create({
        data: {
          userId: teacherUser.id,
          tier: teacher.tier,
          salary: teacher.salary,
          branchId: mainBranch.id,
          isActive: true,
        },
      })
      createdTeachers.push(createdTeacher)
    }
    console.log('👨‍🏫 Created teachers')

    // Create groups
    const groups = [
      { name: 'ENG-ELM-001', courseId: createdCourses[0].id, teacherId: createdTeachers[0].id, maxStudents: 15 },
      { name: 'ENG-INT-001', courseId: createdCourses[1].id, teacherId: createdTeachers[1].id, maxStudents: 12 },
      { name: 'ENG-ADV-001', courseId: createdCourses[2].id, teacherId: createdTeachers[2].id, maxStudents: 10 },
      { name: 'IELTS-001', courseId: createdCourses[3].id, teacherId: createdTeachers[3].id, maxStudents: 8 },
      { name: 'RUS-001', courseId: createdCourses[4].id, teacherId: createdTeachers[4].id, maxStudents: 15 },
    ]

    const createdGroups = []
    for (const group of groups) {
      const createdGroup = await prisma.group.create({
        data: {
          ...group,
          branchId: mainBranch.id,
          status: 'ACTIVE',
          startDate: new Date(),
          endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 3 months later
        },
      })
      createdGroups.push(createdGroup)
    }
    console.log('👥 Created groups')

    // Create students
    const students = [
      { name: 'Alice Cooper', phone: '+998902222221', email: '<EMAIL>', groupId: createdGroups[0].id },
      { name: 'Bob Wilson', phone: '+998902222222', email: '<EMAIL>', groupId: createdGroups[0].id },
      { name: 'Charlie Brown', phone: '+998902222223', email: '<EMAIL>', groupId: createdGroups[1].id },
      { name: 'Diana Prince', phone: '+998902222224', email: '<EMAIL>', groupId: createdGroups[1].id },
      { name: 'Eva Green', phone: '+998902222225', email: '<EMAIL>', groupId: createdGroups[2].id },
      { name: 'Frank Miller', phone: '+998902222226', email: '<EMAIL>', groupId: createdGroups[2].id },
      { name: 'Grace Kelly', phone: '+998902222227', email: '<EMAIL>', groupId: createdGroups[3].id },
      { name: 'Henry Ford', phone: '+998902222228', email: '<EMAIL>', groupId: createdGroups[4].id },
    ]

    const createdStudents = []
    for (const student of students) {
      const studentPassword = await bcrypt.hash('student123', 10)
      const studentUser = await prisma.user.create({
        data: {
          phone: student.phone,
          name: student.name,
          email: student.email,
          role: 'STUDENT',
          password: studentPassword,
        },
      })

      const createdStudent = await prisma.student.create({
        data: {
          userId: studentUser.id,
          groupId: student.groupId,
          branchId: mainBranch.id,
          status: 'ACTIVE',
          enrollmentDate: new Date(),
        },
      })
      createdStudents.push(createdStudent)
    }
    console.log('👨‍🎓 Created students')

    // Create leads
    const leads = [
      { name: 'John Doe', phone: '+998903333331', email: '<EMAIL>', source: 'WEBSITE', status: 'NEW' },
      { name: 'Jane Smith', phone: '+998903333332', email: '<EMAIL>', source: 'REFERRAL', status: 'CONTACTED' },
      { name: 'Mike Johnson', phone: '+998903333333', email: '<EMAIL>', source: 'SOCIAL_MEDIA', status: 'QUALIFIED' },
      { name: 'Lisa Brown', phone: '+998903333334', email: '<EMAIL>', source: 'ADVERTISEMENT', status: 'PROPOSAL_SENT' },
      { name: 'Tom Wilson', phone: '+998903333335', email: '<EMAIL>', source: 'WALK_IN', status: 'NEGOTIATION' },
      { name: 'Anna Davis', phone: '+998903333336', email: '<EMAIL>', source: 'WEBSITE', status: 'CLOSED_WON' },
      { name: 'Peter Parker', phone: '+************', email: '<EMAIL>', source: 'REFERRAL', status: 'CLOSED_LOST' },
    ]

    for (const lead of leads) {
      await prisma.lead.create({
        data: {
          ...lead,
          branchId: mainBranch.id,
          assignedToId: arevikUser.id,
          notes: `Lead from ${lead.source}`,
        },
      })
    }
    console.log('🎯 Created leads')

    // Create payments for students
    const paymentMethods = ['CASH', 'CARD', 'BANK_TRANSFER', 'CLICK', 'PAYME']
    const paymentStatuses = ['PENDING', 'COMPLETED', 'FAILED']

    for (let i = 0; i < createdStudents.length; i++) {
      const student = createdStudents[i]
      const course = createdCourses[i % createdCourses.length]

      // Create 2-3 payments per student
      for (let j = 0; j < Math.floor(Math.random() * 2) + 2; j++) {
        await prisma.payment.create({
          data: {
            studentId: student.id,
            amount: course.price / 3, // Split course price into 3 payments
            method: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
            status: j === 0 ? 'COMPLETED' : paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
            dueDate: new Date(Date.now() + j * 30 * 24 * 60 * 60 * 1000), // Monthly payments
            description: `Payment ${j + 1} for ${course.name}`,
            branchId: mainBranch.id,
          },
        })
      }
    }
    console.log('💰 Created payments')

    // Create attendance records
    for (const student of createdStudents) {
      // Create attendance for last 10 days
      for (let i = 0; i < 10; i++) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
        const isPresent = Math.random() > 0.2 // 80% attendance rate

        await prisma.attendance.create({
          data: {
            studentId: student.id,
            date: date,
            status: isPresent ? 'PRESENT' : 'ABSENT',
            notes: isPresent ? '' : 'Sick leave',
          },
        })
      }
    }
    console.log('📅 Created attendance records')

    console.log('✅ Database seeding completed successfully!')
    return true
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 Starting database reset and seeding process...')
  
  const resetSuccess = await resetDatabase()
  if (!resetSuccess) {
    process.exit(1)
  }

  const seedSuccess = await seedDatabase()
  if (!seedSuccess) {
    process.exit(1)
  }

  console.log('\n🎉 Database reset and seeding completed successfully!')
  console.log('\n📋 Test Users Created:')
  console.log('   Admin: +998901234567 / admin123')
  console.log('   Manager: +998901234568 / manager123')
  console.log('   Arevik (Reception): +998912345678 / Arevik0106$')
  console.log('   Academic Manager: +998901234570 / academic123')
  console.log('   Teachers: +998901111111-115 / teacher123')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
