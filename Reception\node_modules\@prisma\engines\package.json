{"name": "@prisma/engines", "version": "5.6.0", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/engines"}, "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "devDependencies": {"@prisma/engines-version": "5.6.0-32.e95e739751f42d8ca026f6b910f5a2dc5adeaeee", "@swc/core": "1.3.96", "@swc/jest": "0.2.29", "@types/jest": "29.5.8", "@types/node": "18.18.9", "execa": "5.1.1", "jest": "29.7.0", "typescript": "5.2.2", "@prisma/debug": "5.6.0", "@prisma/fetch-engine": "5.6.0", "@prisma/get-platform": "5.6.0"}, "files": ["dist", "download", "scripts"], "sideEffects": false, "scripts": {"dev": "DEV=true node -r esbuild-register helpers/build.ts", "build": "node -r esbuild-register helpers/build.ts", "test": "jest --passWithNoTests", "postinstall": "node scripts/postinstall.js"}}