"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/jwt */ \"(middleware)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Define protected routes and their required roles\nconst protectedRoutes = {\n    '/dashboard': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/analytics': [\n        'ADMIN'\n    ],\n    '/dashboard/users': [\n        'ADMIN'\n    ],\n    '/dashboard/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/dashboard/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER'\n    ],\n    '/dashboard/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/dashboard/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/dashboard/classes': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/dashboard/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/dashboard/communication': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION'\n    ]\n};\n// API routes that require authentication\nconst protectedApiRoutes = {\n    '/api/analytics': [\n        'ADMIN'\n    ],\n    '/api/reports': [\n        'ADMIN'\n    ],\n    '/api/users': [\n        'ADMIN'\n    ],\n    '/api/teachers': [\n        'ADMIN',\n        'MANAGER'\n    ],\n    '/api/students': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'RECEPTION',\n        'CASHIER',\n        'STUDENT',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/groups': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/enrollments': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/payments': [\n        'ADMIN',\n        'CASHIER'\n    ],\n    '/api/attendance': [\n        'MANAGER',\n        'TEACHER'\n    ],\n    '/api/assessments': [\n        'ADMIN',\n        'MANAGER',\n        'TEACHER',\n        'ACADEMIC_MANAGER'\n    ],\n    '/api/leads': [\n        'ADMIN',\n        'MANAGER',\n        'RECEPTION'\n    ],\n    '/api/courses': [\n        'ADMIN',\n        'MANAGER'\n    ]\n};\n// Public routes that don't require authentication\nconst publicRoutes = [\n    '/',\n    '/auth/signin',\n    '/auth/signup',\n    '/auth/error',\n    '/api/auth',\n    '/api/health',\n    '/api/leads',\n    '/api/auth/verify'\n];\n// Specific inter-server routes (more restrictive than wildcard)\nconst interServerRoutes = [\n    '/api/inter-server/health',\n    '/api/inter-server/auth/validate',\n    '/api/inter-server/users'\n];\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files and Next.js internals\n    if (pathname.startsWith('/_next') || pathname.startsWith('/static') || pathname.includes('.') || pathname.startsWith('/favicon')) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Check if route is public\n    const isPublicRoute = publicRoutes.some((route)=>{\n        if (route === pathname) return true;\n        if (route.endsWith('*') && pathname.startsWith(route.slice(0, -1))) return true;\n        return false;\n    });\n    // Check if route is an allowed inter-server route\n    const isInterServerRoute = interServerRoutes.includes(pathname);\n    // Allow public routes and specific inter-server routes\n    if (isPublicRoute || isInterServerRoute) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get the token from the request\n    const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_1__.getToken)({\n        req: request,\n        secret: process.env.NEXTAUTH_SECRET\n    });\n    // Redirect to signin if no token\n    if (!token) {\n        const signInUrl = new URL('/auth/signin', request.url);\n        signInUrl.searchParams.set('callbackUrl', pathname);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(signInUrl);\n    }\n    // Check role-based access for protected routes\n    const userRole = token.role;\n    // Admin server: Only allow ADMIN, MANAGER and CASHIER roles\n    const serverType = process.env.SERVER_TYPE || 'admin';\n    if (serverType === 'admin') {\n        const allowedRoles = [\n            'ADMIN',\n            'MANAGER',\n            'CASHIER'\n        ];\n        if (!allowedRoles.includes(userRole)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/auth/signin?error=unauthorized', request.url));\n        }\n    }\n    // Check dashboard routes\n    for (const [route, allowedRoles] of Object.entries(protectedRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n            }\n            break;\n        }\n    }\n    // Check API routes\n    for (const [route, allowedRoles] of Object.entries(protectedApiRoutes)){\n        if (pathname.startsWith(route)) {\n            if (!allowedRoles.includes(userRole)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized access'\n                }, {\n                    status: 403\n                });\n            }\n            break;\n        }\n    }\n    // Special handling for student access\n    if (userRole === 'STUDENT') {\n        // Students can only access their own data\n        const userId = token.sub;\n        // Allow access to student dashboard\n        if (pathname.startsWith('/dashboard/student')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n        // Restrict access to other dashboard routes\n        if (pathname.startsWith('/dashboard') && pathname !== '/dashboard') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/student', request.url));\n        }\n    }\n    // Special handling for academic manager access\n    if (userRole === 'ACADEMIC_MANAGER') {\n        // Academic managers have access to assessments and test statistics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/assessments',\n            '/dashboard/students'\n        ];\n        const isAllowed = allowedPaths.some((path)=>pathname.startsWith(path));\n        if (!isAllowed && pathname.startsWith('/dashboard')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/assessments', request.url));\n        }\n    }\n    // Teacher-specific restrictions\n    if (userRole === 'TEACHER') {\n        // Teachers can access their assigned groups and students\n        if (pathname.startsWith('/dashboard/teacher')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        }\n    }\n    // Reception-specific restrictions\n    if (userRole === 'RECEPTION') {\n        // Reception can access leads, students, and enrollments\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/leads',\n            '/dashboard/students',\n            '/dashboard/enrollments'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n    }\n    // Cashier-specific restrictions\n    if (userRole === 'CASHIER') {\n        // Cashiers can ONLY access payments and basic student info - NO financial analytics\n        const allowedPaths = [\n            '/dashboard',\n            '/dashboard/payments',\n            '/dashboard/students'\n        ];\n        if (!allowedPaths.some((path)=>pathname.startsWith(path))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard', request.url));\n        }\n        // Block access to any financial analytics or reports\n        if (pathname.includes('/analytics') || pathname.includes('/reports')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/unauthorized', request.url));\n        }\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api/auth (NextAuth.js routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - public folder\r\n     */ '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});