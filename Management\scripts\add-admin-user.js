const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function addAdminUser() {
  console.log('🔧 Adding ADMIN user for Management app...')
  
  try {
    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 10)
    
    const adminUser = await prisma.user.upsert({
      where: { phone: '+998901234567' },
      update: {
        role: 'ADMIN',
        password: adminPassword,
      },
      create: {
        phone: '+998901234567',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'ADMIN',
        password: adminPassword,
      },
    })
    
    console.log('✅ ADMIN user created successfully!')
    console.log('🔑 ADMIN Login Credentials:')
    console.log('   Phone: +998901234567')
    console.log('   Password: admin123')
    console.log('   Role: ADMIN')
    
  } catch (error) {
    console.error('❌ Error adding ADMIN user:', error.message)
  }
}

addAdminUser()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
