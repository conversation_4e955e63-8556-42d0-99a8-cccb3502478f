const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyData() {
  console.log('🔍 Verifying database data...')
  
  try {
    // Count users by role
    const userCounts = await prisma.user.groupBy({
      by: ['role'],
      _count: { role: true }
    })
    
    console.log('\n👥 USERS BY ROLE:')
    userCounts.forEach(role => {
      console.log(`   ${role.role}: ${role._count.role} users`)
    })
    
    // Show sample users with credentials
    const users = await prisma.user.findMany({
      select: {
        name: true,
        phone: true,
        role: true,
        email: true
      }
    })
    
    console.log('\n👤 ALL USERS:')
    users.forEach(user => {
      console.log(`   ${user.name} (${user.role}) - ${user.phone} - ${user.email}`)
    })
    
    console.log('\n🔑 LOGIN CREDENTIALS:')
    console.log('   Manager: +998901234568 / manager123')
    console.log('   Arevik (Reception): +998912345678 / Arevik0106$')
    console.log('   Reception: +998901234569 / reception123')
    console.log('   Teacher: +998905555555 / teacher123')
    console.log('   Student: +998904444444 / student123')
    
    console.log('\n✅ Database verification completed!')
    
  } catch (error) {
    console.error('❌ Database verification failed:', error.message)
  }
}

verifyData()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
