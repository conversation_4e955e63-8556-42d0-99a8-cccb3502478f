{"name": "pg-pool", "version": "3.10.1", "description": "Connection pool for node-postgres", "main": "index.js", "exports": {".": {"import": "./esm/index.mjs", "require": "./index.js", "default": "./index.js"}}, "directories": {"test": "test"}, "scripts": {"test": " node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg-pool"}, "keywords": ["pg", "postgres", "pool", "database"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/brianc/node-pg-pool/issues"}, "homepage": "https://github.com/brianc/node-pg-pool#readme", "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "expect.js": "0.3.1", "lodash": "^4.17.11", "mocha": "^10.5.2"}, "peerDependencies": {"pg": ">=8.0"}, "files": ["index.js", "esm"], "gitHead": "cd877a57612a39335a97b593111710d26126279d"}