"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\n\n//# sourceMappingURL=focusManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const { maxPages } = context.options;\n          const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\n\n//# sourceMappingURL=infiniteQueryBehavior.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\n\n//# sourceMappingURL=mutation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\n\n//# sourceMappingURL=mutationCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL211dGF0aW9uQ2FjaGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNtRDtBQUNWO0FBQ1E7QUFDQTtBQUNqRCxrQ0FBa0MsMERBQVk7QUFDOUMseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGtEQUFRO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix5QkFBeUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMkJBQTJCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSw0REFBYTtBQUNqQjtBQUNBLHNCQUFzQiwyQkFBMkI7QUFDakQsT0FBTztBQUNQO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBLG9CQUFvQix3REFBYTtBQUNqQztBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCLDhDQUE4Qyx3REFBYTtBQUMzRDtBQUNBO0FBQ0EsSUFBSSw0REFBYTtBQUNqQjtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxXQUFXLDREQUFhO0FBQ3hCO0FBQ0Esb0VBQW9FLDJDQUFJO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxDUk0tUGxhdGZvcm1cXE1hbmFnZW1lbnRcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxxdWVyeS1jb3JlXFxidWlsZFxcbW9kZXJuXFxtdXRhdGlvbkNhY2hlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9tdXRhdGlvbkNhY2hlLnRzXG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgTXV0YXRpb24gfSBmcm9tIFwiLi9tdXRhdGlvbi5qc1wiO1xuaW1wb3J0IHsgbWF0Y2hNdXRhdGlvbiwgbm9vcCB9IGZyb20gXCIuL3V0aWxzLmpzXCI7XG5pbXBvcnQgeyBTdWJzY3JpYmFibGUgfSBmcm9tIFwiLi9zdWJzY3JpYmFibGUuanNcIjtcbnZhciBNdXRhdGlvbkNhY2hlID0gY2xhc3MgZXh0ZW5kcyBTdWJzY3JpYmFibGUge1xuICBjb25zdHJ1Y3Rvcihjb25maWcgPSB7fSkge1xuICAgIHN1cGVyKCk7XG4gICAgdGhpcy5jb25maWcgPSBjb25maWc7XG4gICAgdGhpcy4jbXV0YXRpb25zID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgICB0aGlzLiNzY29wZXMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgIHRoaXMuI211dGF0aW9uSWQgPSAwO1xuICB9XG4gICNtdXRhdGlvbnM7XG4gICNzY29wZXM7XG4gICNtdXRhdGlvbklkO1xuICBidWlsZChjbGllbnQsIG9wdGlvbnMsIHN0YXRlKSB7XG4gICAgY29uc3QgbXV0YXRpb24gPSBuZXcgTXV0YXRpb24oe1xuICAgICAgbXV0YXRpb25DYWNoZTogdGhpcyxcbiAgICAgIG11dGF0aW9uSWQ6ICsrdGhpcy4jbXV0YXRpb25JZCxcbiAgICAgIG9wdGlvbnM6IGNsaWVudC5kZWZhdWx0TXV0YXRpb25PcHRpb25zKG9wdGlvbnMpLFxuICAgICAgc3RhdGVcbiAgICB9KTtcbiAgICB0aGlzLmFkZChtdXRhdGlvbik7XG4gICAgcmV0dXJuIG11dGF0aW9uO1xuICB9XG4gIGFkZChtdXRhdGlvbikge1xuICAgIHRoaXMuI211dGF0aW9ucy5hZGQobXV0YXRpb24pO1xuICAgIGNvbnN0IHNjb3BlID0gc2NvcGVGb3IobXV0YXRpb24pO1xuICAgIGlmICh0eXBlb2Ygc2NvcGUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgIGNvbnN0IHNjb3BlZE11dGF0aW9ucyA9IHRoaXMuI3Njb3Blcy5nZXQoc2NvcGUpO1xuICAgICAgaWYgKHNjb3BlZE11dGF0aW9ucykge1xuICAgICAgICBzY29wZWRNdXRhdGlvbnMucHVzaChtdXRhdGlvbik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLiNzY29wZXMuc2V0KHNjb3BlLCBbbXV0YXRpb25dKTtcbiAgICAgIH1cbiAgICB9XG4gICAgdGhpcy5ub3RpZnkoeyB0eXBlOiBcImFkZGVkXCIsIG11dGF0aW9uIH0pO1xuICB9XG4gIHJlbW92ZShtdXRhdGlvbikge1xuICAgIGlmICh0aGlzLiNtdXRhdGlvbnMuZGVsZXRlKG11dGF0aW9uKSkge1xuICAgICAgY29uc3Qgc2NvcGUgPSBzY29wZUZvcihtdXRhdGlvbik7XG4gICAgICBpZiAodHlwZW9mIHNjb3BlID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIGNvbnN0IHNjb3BlZE11dGF0aW9ucyA9IHRoaXMuI3Njb3Blcy5nZXQoc2NvcGUpO1xuICAgICAgICBpZiAoc2NvcGVkTXV0YXRpb25zKSB7XG4gICAgICAgICAgaWYgKHNjb3BlZE11dGF0aW9ucy5sZW5ndGggPiAxKSB7XG4gICAgICAgICAgICBjb25zdCBpbmRleCA9IHNjb3BlZE11dGF0aW9ucy5pbmRleE9mKG11dGF0aW9uKTtcbiAgICAgICAgICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgc2NvcGVkTXV0YXRpb25zLnNwbGljZShpbmRleCwgMSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIGlmIChzY29wZWRNdXRhdGlvbnNbMF0gPT09IG11dGF0aW9uKSB7XG4gICAgICAgICAgICB0aGlzLiNzY29wZXMuZGVsZXRlKHNjb3BlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgdGhpcy5ub3RpZnkoeyB0eXBlOiBcInJlbW92ZWRcIiwgbXV0YXRpb24gfSk7XG4gIH1cbiAgY2FuUnVuKG11dGF0aW9uKSB7XG4gICAgY29uc3Qgc2NvcGUgPSBzY29wZUZvcihtdXRhdGlvbik7XG4gICAgaWYgKHR5cGVvZiBzY29wZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgY29uc3QgbXV0YXRpb25zV2l0aFNhbWVTY29wZSA9IHRoaXMuI3Njb3Blcy5nZXQoc2NvcGUpO1xuICAgICAgY29uc3QgZmlyc3RQZW5kaW5nTXV0YXRpb24gPSBtdXRhdGlvbnNXaXRoU2FtZVNjb3BlPy5maW5kKFxuICAgICAgICAobSkgPT4gbS5zdGF0ZS5zdGF0dXMgPT09IFwicGVuZGluZ1wiXG4gICAgICApO1xuICAgICAgcmV0dXJuICFmaXJzdFBlbmRpbmdNdXRhdGlvbiB8fCBmaXJzdFBlbmRpbmdNdXRhdGlvbiA9PT0gbXV0YXRpb247XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuICBydW5OZXh0KG11dGF0aW9uKSB7XG4gICAgY29uc3Qgc2NvcGUgPSBzY29wZUZvcihtdXRhdGlvbik7XG4gICAgaWYgKHR5cGVvZiBzY29wZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgY29uc3QgZm91bmRNdXRhdGlvbiA9IHRoaXMuI3Njb3Blcy5nZXQoc2NvcGUpPy5maW5kKChtKSA9PiBtICE9PSBtdXRhdGlvbiAmJiBtLnN0YXRlLmlzUGF1c2VkKTtcbiAgICAgIHJldHVybiBmb3VuZE11dGF0aW9uPy5jb250aW51ZSgpID8/IFByb21pc2UucmVzb2x2ZSgpO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgfVxuICB9XG4gIGNsZWFyKCkge1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgdGhpcy4jbXV0YXRpb25zLmZvckVhY2goKG11dGF0aW9uKSA9PiB7XG4gICAgICAgIHRoaXMubm90aWZ5KHsgdHlwZTogXCJyZW1vdmVkXCIsIG11dGF0aW9uIH0pO1xuICAgICAgfSk7XG4gICAgICB0aGlzLiNtdXRhdGlvbnMuY2xlYXIoKTtcbiAgICAgIHRoaXMuI3Njb3Blcy5jbGVhcigpO1xuICAgIH0pO1xuICB9XG4gIGdldEFsbCgpIHtcbiAgICByZXR1cm4gQXJyYXkuZnJvbSh0aGlzLiNtdXRhdGlvbnMpO1xuICB9XG4gIGZpbmQoZmlsdGVycykge1xuICAgIGNvbnN0IGRlZmF1bHRlZEZpbHRlcnMgPSB7IGV4YWN0OiB0cnVlLCAuLi5maWx0ZXJzIH07XG4gICAgcmV0dXJuIHRoaXMuZ2V0QWxsKCkuZmluZChcbiAgICAgIChtdXRhdGlvbikgPT4gbWF0Y2hNdXRhdGlvbihkZWZhdWx0ZWRGaWx0ZXJzLCBtdXRhdGlvbilcbiAgICApO1xuICB9XG4gIGZpbmRBbGwoZmlsdGVycyA9IHt9KSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0QWxsKCkuZmlsdGVyKChtdXRhdGlvbikgPT4gbWF0Y2hNdXRhdGlvbihmaWx0ZXJzLCBtdXRhdGlvbikpO1xuICB9XG4gIG5vdGlmeShldmVudCkge1xuICAgIG5vdGlmeU1hbmFnZXIuYmF0Y2goKCkgPT4ge1xuICAgICAgdGhpcy5saXN0ZW5lcnMuZm9yRWFjaCgobGlzdGVuZXIpID0+IHtcbiAgICAgICAgbGlzdGVuZXIoZXZlbnQpO1xuICAgICAgfSk7XG4gICAgfSk7XG4gIH1cbiAgcmVzdW1lUGF1c2VkTXV0YXRpb25zKCkge1xuICAgIGNvbnN0IHBhdXNlZE11dGF0aW9ucyA9IHRoaXMuZ2V0QWxsKCkuZmlsdGVyKCh4KSA9PiB4LnN0YXRlLmlzUGF1c2VkKTtcbiAgICByZXR1cm4gbm90aWZ5TWFuYWdlci5iYXRjaChcbiAgICAgICgpID0+IFByb21pc2UuYWxsKFxuICAgICAgICBwYXVzZWRNdXRhdGlvbnMubWFwKChtdXRhdGlvbikgPT4gbXV0YXRpb24uY29udGludWUoKS5jYXRjaChub29wKSlcbiAgICAgIClcbiAgICApO1xuICB9XG59O1xuZnVuY3Rpb24gc2NvcGVGb3IobXV0YXRpb24pIHtcbiAgcmV0dXJuIG11dGF0aW9uLm9wdGlvbnMuc2NvcGU/LmlkO1xufVxuZXhwb3J0IHtcbiAgTXV0YXRpb25DYWNoZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW11dGF0aW9uQ2FjaGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   defaultScheduler: () => (/* binding */ defaultScheduler),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nvar defaultScheduler = (cb) => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\n\n//# sourceMappingURL=notifyManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\n\n//# sourceMappingURL=onlineManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveStaleTime)(observer.options.staleTime, this) === \"static\"\n      );\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (true) {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\n\n//# sourceMappingURL=query.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=queryCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n    this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled() && !query.isStatic()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\n\n//# sourceMappingURL=queryClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\n\n//# sourceMappingURL=removable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3JlbW92YWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ3NEO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5REFBYztBQUN0QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsK0NBQVE7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxDUk0tUGxhdGZvcm1cXE1hbmFnZW1lbnRcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxxdWVyeS1jb3JlXFxidWlsZFxcbW9kZXJuXFxyZW1vdmFibGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3JlbW92YWJsZS50c1xuaW1wb3J0IHsgaXNTZXJ2ZXIsIGlzVmFsaWRUaW1lb3V0IH0gZnJvbSBcIi4vdXRpbHMuanNcIjtcbnZhciBSZW1vdmFibGUgPSBjbGFzcyB7XG4gICNnY1RpbWVvdXQ7XG4gIGRlc3Ryb3koKSB7XG4gICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICB9XG4gIHNjaGVkdWxlR2MoKSB7XG4gICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICAgIGlmIChpc1ZhbGlkVGltZW91dCh0aGlzLmdjVGltZSkpIHtcbiAgICAgIHRoaXMuI2djVGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB0aGlzLm9wdGlvbmFsUmVtb3ZlKCk7XG4gICAgICB9LCB0aGlzLmdjVGltZSk7XG4gICAgfVxuICB9XG4gIHVwZGF0ZUdjVGltZShuZXdHY1RpbWUpIHtcbiAgICB0aGlzLmdjVGltZSA9IE1hdGgubWF4KFxuICAgICAgdGhpcy5nY1RpbWUgfHwgMCxcbiAgICAgIG5ld0djVGltZSA/PyAoaXNTZXJ2ZXIgPyBJbmZpbml0eSA6IDUgKiA2MCAqIDFlMylcbiAgICApO1xuICB9XG4gIGNsZWFyR2NUaW1lb3V0KCkge1xuICAgIGlmICh0aGlzLiNnY1RpbWVvdXQpIHtcbiAgICAgIGNsZWFyVGltZW91dCh0aGlzLiNnY1RpbWVvdXQpO1xuICAgICAgdGhpcy4jZ2NUaW1lb3V0ID0gdm9pZCAwO1xuICAgIH1cbiAgfVxufTtcbmV4cG9ydCB7XG4gIFJlbW92YWJsZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlbW92YWJsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\n\n//# sourceMappingURL=retryer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\n\n//# sourceMappingURL=subscribable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXENSTS1QbGF0Zm9ybVxcTWFuYWdlbWVudFxcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHF1ZXJ5LWNvcmVcXGJ1aWxkXFxtb2Rlcm5cXHN1YnNjcmliYWJsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvc3Vic2NyaWJhYmxlLnRzXG52YXIgU3Vic2NyaWJhYmxlID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmxpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gICAgdGhpcy5zdWJzY3JpYmUgPSB0aGlzLnN1YnNjcmliZS5iaW5kKHRoaXMpO1xuICB9XG4gIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICAgIHRoaXMubGlzdGVuZXJzLmFkZChsaXN0ZW5lcik7XG4gICAgdGhpcy5vblN1YnNjcmliZSgpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB0aGlzLmxpc3RlbmVycy5kZWxldGUobGlzdGVuZXIpO1xuICAgICAgdGhpcy5vblVuc3Vic2NyaWJlKCk7XG4gICAgfTtcbiAgfVxuICBoYXNMaXN0ZW5lcnMoKSB7XG4gICAgcmV0dXJuIHRoaXMubGlzdGVuZXJzLnNpemUgPiAwO1xuICB9XG4gIG9uU3Vic2NyaWJlKCkge1xuICB9XG4gIG9uVW5zdWJzY3JpYmUoKSB7XG4gIH1cbn07XG5leHBvcnQge1xuICBTdWJzY3JpYmFibGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdWJzY3JpYmFibGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable),\n/* harmony export */   tryResolveSync: () => (/* binding */ tryResolveSync)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/thenable.ts\n\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  }, _utils_js__WEBPACK_IMPORTED_MODULE_0__.noop)?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_0__.noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\n\n//# sourceMappingURL=thenable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    const aItemsSet = new Set(aItems);\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (true) {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (true) {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"QueryClientProvider.useEffect\": ()=>{\n            client.mount();\n            return ({\n                \"QueryClientProvider.useEffect\": ()=>{\n                    client.unmount();\n                }\n            })[\"QueryClientProvider.useEffect\"];\n        }\n    }[\"QueryClientProvider.useEffect\"], [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ })

};
;