const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyData() {
  console.log('🔍 Verifying Reception app database access...')
  
  try {
    // Count users by role
    const userCounts = await prisma.user.groupBy({
      by: ['role'],
      _count: { role: true }
    })
    
    console.log('\n👥 USERS BY ROLE:')
    userCounts.forEach(role => {
      console.log(`   ${role.role}: ${role._count.role} users`)
    })
    
    console.log('\n✅ Reception app can access database successfully!')
    
  } catch (error) {
    console.error('❌ Reception app database access failed:', error.message)
  }
}

verifyData()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
