{"name": "@prisma/client", "version": "5.6.0", "description": "Prisma Client is an auto-generated, type-safe and modern JavaScript/TypeScript ORM for Node.js that's tailored to your data. Supports PostgreSQL, CockroachDB, MySQL, MariaDB, SQL Server, SQLite & MongoDB databases.", "keywords": ["ORM", "Prisma", "prisma2", "Prisma Client", "client", "query", "query-builder", "database", "db", "JavaScript", "JS", "TypeScript", "TS", "SQL", "SQLite", "pg", "Postgres", "PostgreSQL", "CockroachDB", "MySQL", "MariaDB", "MSSQL", "SQL Server", "SQLServer", "MongoDB"], "main": "index.js", "browser": "index-browser.js", "types": "index.d.ts", "license": "Apache-2.0", "engines": {"node": ">=16.13"}, "homepage": "https://www.prisma.io", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/client"}, "author": "<PERSON> <suchane<PERSON>@prisma.io>", "bugs": "https://github.com/prisma/prisma/issues", "files": ["README.md", "runtime", "!runtime/*.map", "scripts", "generator-build", "edge.js", "edge.d.ts", "index.js", "index.d.ts", "index-browser.js", "extension.js", "extension.d.ts"], "devDependencies": {"@codspeed/benchmark.js-plugin": "2.2.0", "@faker-js/faker": "8.2.0", "@fast-check/jest": "1.7.3", "@jest/create-cache-key-function": "29.7.0", "@jest/globals": "29.7.0", "@jest/test-sequencer": "29.7.0", "@libsql/client": "0.3.6", "@neondatabase/serverless": "0.6.0", "@opentelemetry/api": "1.7.0", "@opentelemetry/context-async-hooks": "1.18.0", "@opentelemetry/instrumentation": "0.45.0", "@opentelemetry/resources": "1.18.0", "@opentelemetry/sdk-trace-base": "1.18.0", "@opentelemetry/semantic-conventions": "1.18.0", "@planetscale/database": "1.11.0", "@prisma/mini-proxy": "0.9.4", "@prisma/query-engine-wasm": "0.0.5", "@snaplet/copycat": "0.17.3", "@swc-node/register": "1.6.8", "@swc/core": "1.3.96", "@swc/jest": "0.2.29", "@timsuchanek/copy": "1.4.5", "@types/debug": "4.1.12", "@types/fs-extra": "9.0.13", "@types/jest": "29.5.8", "@types/js-levenshtein": "1.1.3", "@types/mssql": "9.1.3", "@types/node": "18.18.9", "@types/node-fetch": "2.6.9", "@types/pg": "8.10.9", "@types/yeoman-generator": "5.2.14", "arg": "5.0.2", "benchmark": "2.1.4", "ci-info": "4.0.0", "decimal.js": "10.4.3", "detect-runtime": "1.0.4", "env-paths": "2.2.1", "esbuild": "0.19.5", "execa": "5.1.1", "expect-type": "0.16.0", "flat-map-polyfill": "0.3.8", "fs-extra": "11.1.1", "get-stream": "6.0.1", "globby": "11.1.0", "indent-string": "4.0.0", "jest": "29.7.0", "jest-extended": "4.0.2", "jest-junit": "16.0.0", "jest-serializer-ansi-escapes": "2.0.1", "jest-snapshot": "29.7.0", "js-levenshtein": "1.1.6", "kleur": "4.1.5", "klona": "2.0.6", "mariadb": "3.2.2", "memfs": "4.6.0", "mssql": "10.0.1", "new-github-issue-url": "0.2.1", "node-fetch": "2.7.0", "p-retry": "4.6.2", "pg": "8.11.3", "pkg-up": "3.1.0", "pluralize": "8.0.0", "resolve": "1.22.8", "rimraf": "3.0.2", "simple-statistics": "7.8.3", "sort-keys": "4.2.0", "source-map-support": "0.5.21", "sql-template-tag": "5.1.0", "stacktrace-parser": "0.1.10", "strip-ansi": "6.0.1", "strip-indent": "3.0.0", "ts-node": "10.9.1", "ts-pattern": "5.0.5", "tsd": "0.29.0", "typescript": "5.2.2", "undici": "5.27.2", "yeoman-generator": "5.10.0", "yo": "4.3.1", "zx": "7.2.3", "@prisma/adapter-libsql": "5.6.0", "@prisma/adapter-neon": "5.6.0", "@prisma/adapter-pg": "5.6.0", "@prisma/adapter-planetscale": "5.6.0", "@prisma/debug": "5.6.0", "@prisma/driver-adapter-utils": "5.6.0", "@prisma/engines": "5.6.0", "@prisma/fetch-engine": "5.6.0", "@prisma/generator-helper": "5.6.0", "@prisma/get-platform": "5.6.0", "@prisma/instrumentation": "5.6.0", "@prisma/internals": "5.6.0", "@prisma/migrate": "5.6.0"}, "peerDependencies": {"prisma": "*"}, "peerDependenciesMeta": {"prisma": {"optional": true}}, "dependencies": {"@prisma/engines-version": "5.6.0-32.e95e739751f42d8ca026f6b910f5a2dc5adeaeee"}, "sideEffects": false, "scripts": {"dev": "DEV=true node -r esbuild-register helpers/build.ts", "build": "node -r esbuild-register helpers/build.ts", "test": "dotenv -e ../../.db.env -- jest --silent", "test:e2e": "dotenv -e ../../.db.env -- node -r esbuild-register tests/e2e/_utils/run.ts", "test:functional": "dotenv -e ../../.db.env -- node -r esbuild-register helpers/functional-test/run-tests.ts", "test:memory": "dotenv -e ../../.db.env -- node -r esbuild-register helpers/memory-tests.ts", "test:functional:code": "dotenv -e ../../.db.env -- node -r esbuild-register helpers/functional-test/run-tests.ts --no-types", "test:functional:types": "dotenv -e ../../.db.env -- node -r esbuild-register helpers/functional-test/run-tests.ts --types-only", "test-notypes": "dotenv -e ../../.db.env -- jest --testPathIgnorePatterns src/__tests__/types/types.test.ts", "generate": "node scripts/postinstall.js", "postinstall": "node scripts/postinstall.js", "new-test": "NODE_OPTIONS='-r esbuild-register' yo ./helpers/generator-test/index.ts"}}